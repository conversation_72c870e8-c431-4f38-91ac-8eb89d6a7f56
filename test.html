<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار نظام إدارة العيادات</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            justify-content: center;
            align-items: center;
            direction: rtl;
        }
        
        .container {
            background: white;
            padding: 40px;
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
            max-width: 600px;
            width: 90%;
        }
        
        .logo {
            width: 80px;
            height: 80px;
            background: linear-gradient(135deg, #0ea5e9, #3b82f6);
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 30px;
        }
        
        h1 {
            color: #1e293b;
            margin-bottom: 10px;
            font-size: 28px;
        }
        
        .subtitle {
            color: #64748b;
            margin-bottom: 30px;
            font-size: 16px;
        }
        
        .status {
            background: #f0f9ff;
            border: 2px solid #0ea5e9;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status h3 {
            color: #0ea5e9;
            margin-bottom: 10px;
        }
        
        .instructions {
            background: #f8fafc;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: right;
        }
        
        .instructions h3 {
            color: #1e293b;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .instructions ol {
            color: #475569;
            line-height: 1.8;
        }
        
        .instructions li {
            margin-bottom: 8px;
        }
        
        .button {
            background: linear-gradient(135deg, #0ea5e9, #3b82f6);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 10px;
            font-size: 16px;
            cursor: pointer;
            margin: 10px;
            transition: transform 0.2s;
        }
        
        .button:hover {
            transform: translateY(-2px);
        }
        
        .code {
            background: #1e293b;
            color: #e2e8f0;
            padding: 15px;
            border-radius: 8px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            text-align: left;
            direction: ltr;
        }
        
        .warning {
            background: #fef3c7;
            border: 2px solid #f59e0b;
            border-radius: 10px;
            padding: 15px;
            margin: 20px 0;
            color: #92400e;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">🏥</div>
        <h1>نظام إدارة العيادات الطبية</h1>
        <p class="subtitle">مركز د. حمدي عواد الطبي</p>
        
        <div class="status">
            <h3>✅ الملفات جاهزة للتشغيل</h3>
            <p>تم إنشاء جميع ملفات النظام بنجاح</p>
        </div>
        
        <div class="instructions">
            <h3>خطوات التشغيل</h3>
            <ol>
                <li>تأكد من تثبيت Node.js على جهازك</li>
                <li>افتح Terminal أو Command Prompt في مجلد المشروع</li>
                <li>شغل الأوامر التالية:</li>
            </ol>
            
            <div class="code">
npm install<br>
npm run dev
            </div>
            
            <p style="margin-top: 15px; color: #059669;">
                <strong>أو استخدم ملفات التشغيل السريع:</strong><br>
                Windows: انقر مرتين على start.bat<br>
                Mac/Linux: شغل ./start.sh
            </p>
        </div>
        
        <div class="warning">
            <strong>⚠️ ملاحظة مهمة:</strong><br>
            هذا ملف اختبار فقط. لتشغيل النظام الفعلي، استخدم الأوامر أعلاه.
        </div>
        
        <button class="button" onclick="showMore()">عرض المزيد من التفاصيل</button>
        <button class="button" onclick="window.open('README.md')">دليل المستخدم</button>
    </div>
    
    <script>
        function showMore() {
            alert(`
🎉 مميزات النظام:

✅ سلة الخدمات الاحترافية
✅ إدارة المرضى والأطباء  
✅ نظام الحجوزات والبصمة
✅ التقارير المالية الشاملة
✅ التكامل مع Google Sheets
✅ تصميم عربي احترافي

📞 للدعم التقني:
راجع ملف README.md أو QUICK_START.md
            `);
        }
        
        // Animation
        document.addEventListener('DOMContentLoaded', function() {
            const container = document.querySelector('.container');
            container.style.opacity = '0';
            container.style.transform = 'translateY(30px)';
            
            setTimeout(() => {
                container.style.transition = 'all 0.6s ease';
                container.style.opacity = '1';
                container.style.transform = 'translateY(0)';
            }, 100);
        });
    </script>
</body>
</html>
