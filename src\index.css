@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Base styles */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html {
  direction: rtl;
  font-family: 'Cairo', 'Ta<PERSON><PERSON>', sans-serif;
}

body {
  font-family: 'Cairo', 'Tajawal', sans-serif;
  direction: rtl;
  background-color: #f8fafc;
  color: #1e293b;
  line-height: 1.6;
}

/* Component styles */
.btn-primary {
  background-color: #0284c7;
  color: white;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-primary:hover {
  background-color: #0369a1;
}

.btn-secondary {
  background-color: #e2e8f0;
  color: #334155;
  padding: 0.5rem 1rem;
  border-radius: 0.5rem;
  border: none;
  cursor: pointer;
  font-weight: 500;
  transition: background-color 0.2s;
}

.btn-secondary:hover {
  background-color: #cbd5e1;
}

.card {
  background-color: white;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
  border: 1px solid #e2e8f0;
  padding: 1.5rem;
}

.input-field {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid #cbd5e1;
  border-radius: 0.5rem;
  outline: none;
  transition: border-color 0.2s, box-shadow 0.2s;
}

.input-field:focus {
  border-color: #0ea5e9;
  box-shadow: 0 0 0 3px rgba(14, 165, 233, 0.1);
}

.sidebar-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  color: #475569;
  border-radius: 0.5rem;
  cursor: pointer;
  transition: all 0.2s;
  text-decoration: none;
}

.sidebar-item:hover {
  background-color: #f0f9ff;
  color: #0369a1;
}

.sidebar-item.active {
  background-color: #dbeafe;
  color: #0369a1;
  border-right: 4px solid #0284c7;
}

/* Utility classes */
.text-primary-600 { color: #0284c7; }
.text-primary-700 { color: #0369a1; }
.text-primary-800 { color: #075985; }
.text-secondary-600 { color: #475569; }
.text-secondary-700 { color: #334155; }
.text-secondary-800 { color: #1e293b; }
.text-green-600 { color: #16a34a; }
.text-red-600 { color: #dc2626; }
.text-blue-600 { color: #2563eb; }
.text-yellow-600 { color: #ca8a04; }

.bg-primary-50 { background-color: #f0f9ff; }
.bg-primary-100 { background-color: #e0f2fe; }
.bg-primary-600 { background-color: #0284c7; }
.bg-secondary-50 { background-color: #f8fafc; }
.bg-secondary-100 { background-color: #f1f5f9; }
.bg-secondary-200 { background-color: #e2e8f0; }

.border-primary-200 { border-color: #bae6fd; }
.border-secondary-200 { border-color: #e2e8f0; }
.border-secondary-300 { border-color: #cbd5e1; }
