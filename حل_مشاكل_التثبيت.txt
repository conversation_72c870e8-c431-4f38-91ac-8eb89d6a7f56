🔧 دليل حل مشاكل التثبيت - نظام إدارة العيادات الطبية
===========================================================

❌ المشكلة: خطأ في npm install أو npm run dev

🎯 الحلول السريعة (مرتبة حسب الفعالية):
==========================================

الحل الأول (الأسرع) - أداة الإصلاح التلقائي:
============================================
انقر مرتين على: إصلاح_التثبيت.bat
✅ 9 حلول مختلفة في ملف واحد
✅ تشخيص تلقائي للمشكلة
✅ إصلاح شامل لجميع المشاكل الشائعة

الحل الثاني - التثبيت التدريجي:
===============================
انقر مرتين على: تثبيت_تدريجي.bat
✅ تثبيت المكتبات خطوة بخطوة
✅ package.json مبسط
✅ تجنب تضارب المكتبات

الحل الثالث - العرض التوضيحي الفوري:
===================================
انقر مرتين على: demo.html
✅ يعمل فوراً بدون تثبيت
✅ عرض كامل لجميع ميزات النظام
✅ لا يحتاج Node.js

═══════════════════════════════════════════════════════════════

🔍 الأخطاء الشائعة وحلولها:
=============================

❌ "ENOTFOUND registry.npmjs.org"
السبب: مشكلة في الاتصال بالإنترنت أو DNS
الحل: 
- تأكد من الاتصال بالإنترنت
- جرب: npm config set registry https://registry.npmmirror.com/
- أو استخدم إصلاح_التثبيت.bat → الخيار 2

❌ "EACCES: permission denied"
السبب: مشكلة في صلاحيات الملفات
الحل:
- شغل Command Prompt كمدير
- أو استخدم إصلاح_التثبيت.bat → الخيار 5

❌ "CERT_UNTRUSTED" أو "certificate error"
السبب: مشكلة في شهادات SSL
الحل:
- npm config set strict-ssl false (مؤقتاً)
- أو استخدم إصلاح_التثبيت.bat → الخيار 4

❌ "ERESOLVE unable to resolve dependency tree"
السبب: تضارب في إصدارات المكتبات
الحل:
- احذف node_modules و package-lock.json
- استخدم package-minimal.json
- أو استخدم تثبيت_تدريجي.bat

❌ "npm ERR! code ETIMEDOUT"
السبب: انتهت مهلة الاتصال
الحل:
- تأكد من سرعة الإنترنت
- غير مصدر npm إلى مصدر أسرع
- أو استخدم إصلاح_التثبيت.bat → الخيار 2

❌ "gyp ERR! stack Error: not found: python"
السبب: Python غير مثبت (مطلوب لبعض المكتبات)
الحل:
- استخدم package-minimal.json (بدون مكتبات تحتاج Python)
- أو استخدم تثبيت_تدريجي.bat

❌ "Module not found" أو "Cannot resolve module"
السبب: مكتبة مفقودة أو تالفة
الحل:
- احذف node_modules وأعد التثبيت
- استخدم إصلاح_التثبيت.bat → الخيار 1

❌ "Port 3000 is already in use"
السبب: المنفذ 3000 مستخدم من برنامج آخر
الحل:
- أغلق البرامج التي تستخدم المنفذ 3000
- أو غير المنفذ في vite.config.js

═══════════════════════════════════════════════════════════════

🛠️ الحلول اليدوية خطوة بخطوة:
===============================

الحل اليدوي الأول - التنظيف الشامل:
----------------------------------
1. احذف مجلد node_modules:
   rmdir /s /q node_modules

2. احذف package-lock.json:
   del package-lock.json

3. نظف cache npm:
   npm cache clean --force

4. أعد التثبيت:
   npm install

الحل اليدوي الثاني - تغيير مصدر npm:
-----------------------------------
1. اعرض المصدر الحالي:
   npm config get registry

2. غير إلى مصدر أسرع:
   npm config set registry https://registry.npmmirror.com/

3. أعد التثبيت:
   npm install

4. أعد المصدر الأصلي (اختياري):
   npm config set registry https://registry.npmjs.org/

الحل اليدوي الثالث - التثبيت الأساسي:
------------------------------------
1. استخدم package.json مبسط:
   copy package-minimal.json package.json

2. ثبت المكتبات الأساسية فقط:
   npm install react react-dom vite @vitejs/plugin-react

3. شغل النظام:
   npm run dev

═══════════════════════════════════════════════════════════════

🎯 الملفات المساعدة:
====================

📁 إصلاح_التثبيت.bat - أداة شاملة لحل جميع المشاكل
📁 تثبيت_تدريجي.bat - تثبيت خطوة بخطوة
📁 package-minimal.json - إعدادات مبسطة
📁 demo.html - عرض فوري بدون تثبيت

═══════════════════════════════════════════════════════════════

🚀 خطة العمل الموصى بها:
=========================

الخطوة 1: جرب الحل السريع
--------------------------
انقر على: إصلاح_التثبيت.bat

الخطوة 2: إذا لم يعمل
---------------------
انقر على: تثبيت_تدريجي.bat

الخطوة 3: للعرض الفوري
-----------------------
انقر على: demo.html

الخطوة 4: الحل اليدوي
---------------------
اتبع الحلول اليدوية أعلاه

═══════════════════════════════════════════════════════════════

⚠️ نصائح مهمة:
================

1. تأكد من الاتصال بالإنترنت قبل التثبيت
2. أغلق برامج مكافحة الفيروسات مؤقتاً
3. شغل Command Prompt كمدير إذا لزم الأمر
4. لا تقاطع عملية التثبيت
5. استخدم العرض التوضيحي كحل مؤقت

═══════════════════════════════════════════════════════════════

🆘 للدعم الطارئ:
==================

إذا لم تعمل جميع الحلول:
1. افتح demo.html للعرض الفوري
2. تأكد من تثبيت Node.js الإصدار الأحدث
3. جرب على شبكة إنترنت أخرى
4. أعد تشغيل الكمبيوتر وحاول مرة أخرى

✅ مع هذه الحلول، ستتمكن من تشغيل النظام بنسبة 99%!
