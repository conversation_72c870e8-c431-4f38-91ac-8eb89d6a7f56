@echo off
chcp 65001 >nul
color 0C
title نظام العيادات - تشغيل الطوارئ

echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                           تشغيل الطوارئ                            ║
echo ║                    عندما لا يعمل أي شيء آخر                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🆘 مرحباً بك في وضع الطوارئ
echo.

echo 🔍 جاري فحص الملفات المتاحة...
echo.

REM فحص العرض التوضيحي
if exist demo.html (
    echo ✅ العرض التوضيحي متوفر
    set demo_available=1
) else (
    echo ❌ العرض التوضيحي غير متوفر
    set demo_available=0
)

REM فحص ملف الاختبار
if exist test.html (
    echo ✅ ملف الاختبار متوفر
    set test_available=1
) else (
    echo ❌ ملف الاختبار غير متوفر
    set test_available=0
)

REM فحص Node.js
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js متوفر
    set nodejs_available=1
) else (
    echo ❌ Node.js غير متوفر
    set nodejs_available=0
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                            خيارات الطوارئ
echo ═══════════════════════════════════════════════════════════════════════
echo.

if %demo_available%==1 (
    echo [1] 🎭 فتح العرض التوضيحي الكامل
)

if %test_available%==1 (
    echo [2] 🧪 فتح صفحة الاختبار
)

if %nodejs_available%==1 (
    echo [3] 🔧 محاولة إصلاح وتشغيل
    echo [4] 🧹 تنظيف شامل وإعادة تثبيت
)

echo [5] 📋 إنشاء عرض بسيط
echo [6] 📖 عرض التعليمات
echo [7] 🌐 فتح مواقع مفيدة
echo [8] ❌ خروج
echo.

set /p choice="اختر الخيار المناسب: "

if "%choice%"=="1" if %demo_available%==1 goto :open_demo
if "%choice%"=="2" if %test_available%==1 goto :open_test
if "%choice%"=="3" if %nodejs_available%==1 goto :try_fix
if "%choice%"=="4" if %nodejs_available%==1 goto :clean_all
if "%choice%"=="5" goto :create_simple
if "%choice%"=="6" goto :show_help
if "%choice%"=="7" goto :open_websites
if "%choice%"=="8" goto :exit

echo ❌ خيار غير متاح أو غير صحيح
timeout /t 2 >nul
goto :main

:open_demo
echo.
echo 🎭 جاري فتح العرض التوضيحي...
start demo.html
echo ✅ تم فتح العرض في المتصفح
goto :success

:open_test
echo.
echo 🧪 جاري فتح صفحة الاختبار...
start test.html
echo ✅ تم فتح صفحة الاختبار
goto :success

:try_fix
echo.
echo 🔧 محاولة الإصلاح والتشغيل...
echo.

echo جاري حذف الملفات المؤقتة...
if exist package-lock.json del package-lock.json
if exist node_modules rmdir /s /q node_modules

echo جاري تثبيت المكتبات الأساسية فقط...
npm install react react-dom react-router-dom vite @vitejs/plugin-react

if %errorlevel% equ 0 (
    echo ✅ تم التثبيت، جاري التشغيل...
    npm run dev
) else (
    echo ❌ فشل الإصلاح
    if %demo_available%==1 (
        echo جاري فتح العرض التوضيحي كبديل...
        start demo.html
    )
)
goto :success

:clean_all
echo.
echo 🧹 تنظيف شامل...
echo.

echo حذف جميع الملفات المؤقتة...
if exist node_modules (
    echo جاري حذف node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json del package-lock.json
if exist .vite rmdir /s /q .vite
if exist dist rmdir /s /q dist

echo تنظيف cache npm...
npm cache clean --force

echo إعادة تثبيت كاملة...
npm install

if %errorlevel% equ 0 (
    echo ✅ تم التنظيف والتثبيت، جاري التشغيل...
    npm run dev
) else (
    echo ❌ فشل التنظيف الشامل
)
goto :success

:create_simple
echo.
echo 📋 جاري إنشاء عرض بسيط...

echo ^<!DOCTYPE html^> > عرض_بسيط.html
echo ^<html lang="ar" dir="rtl"^> >> عرض_بسيط.html
echo ^<head^> >> عرض_بسيط.html
echo ^<meta charset="UTF-8"^> >> عرض_بسيط.html
echo ^<title^>نظام إدارة العيادات الطبية^</title^> >> عرض_بسيط.html
echo ^<style^> >> عرض_بسيط.html
echo body { font-family: Arial; text-align: center; padding: 50px; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); color: white; } >> عرض_بسيط.html
echo .container { background: white; color: #333; padding: 40px; border-radius: 20px; max-width: 600px; margin: 0 auto; } >> عرض_بسيط.html
echo h1 { color: #2563eb; margin-bottom: 20px; } >> عرض_بسيط.html
echo .feature { background: #f8fafc; padding: 15px; margin: 10px 0; border-radius: 10px; } >> عرض_بسيط.html
echo ^</style^> >> عرض_بسيط.html
echo ^</head^> >> عرض_بسيط.html
echo ^<body^> >> عرض_بسيط.html
echo ^<div class="container"^> >> عرض_بسيط.html
echo ^<h1^>🏥 نظام إدارة العيادات الطبية^</h1^> >> عرض_بسيط.html
echo ^<h2^>مركز د. حمدي عواد الطبي^</h2^> >> عرض_بسيط.html
echo ^<div class="feature"^>✅ سلة الخدمات الاحترافية^</div^> >> عرض_بسيط.html
echo ^<div class="feature"^>✅ إدارة المرضى والأطباء^</div^> >> عرض_بسيط.html
echo ^<div class="feature"^>✅ نظام الحجوزات والبصمة^</div^> >> عرض_بسيط.html
echo ^<div class="feature"^>✅ التقارير المالية الشاملة^</div^> >> عرض_بسيط.html
echo ^<div class="feature"^>✅ التكامل مع Google Sheets^</div^> >> عرض_بسيط.html
echo ^<p^>للحصول على النظام الكامل، يرجى تثبيت Node.js وتشغيل الملفات المناسبة^</p^> >> عرض_بسيط.html
echo ^</div^> >> عرض_بسيط.html
echo ^</body^> >> عرض_بسيط.html
echo ^</html^> >> عرض_بسيط.html

start عرض_بسيط.html
echo ✅ تم إنشاء وفتح عرض بسيط
goto :success

:show_help
echo.
echo 📖 تعليمات الطوارئ:
echo.
echo 🔧 إذا كان لديك مشاكل في التشغيل:
echo 1. تأكد من تثبيت Node.js من nodejs.org
echo 2. استخدم start-cmd.bat بدلاً من start.bat
echo 3. جرب تشغيل_سريع.bat
echo.
echo 🎭 للعرض الفوري:
echo 1. افتح demo.html في أي متصفح
echo 2. أو استخدم الخيار 5 لإنشاء عرض بسيط
echo.
echo 🆘 للدعم:
echo 1. راجع ملف README.md
echo 2. راجع ملف حل_مشكلة_PowerShell.txt
echo 3. راجع ملف حل_مشكلة_CSS.txt
echo.
pause
goto :main

:open_websites
echo.
echo 🌐 فتح مواقع مفيدة...
echo.

echo [1] تحميل Node.js
echo [2] دليل React
echo [3] دليل Vite
echo [4] العودة
echo.

set /p web_choice="اختر الموقع: "

if "%web_choice%"=="1" (
    start https://nodejs.org
    echo ✅ تم فتح موقع Node.js
)

if "%web_choice%"=="2" (
    start https://react.dev
    echo ✅ تم فتح دليل React
)

if "%web_choice%"=="3" (
    start https://vitejs.dev
    echo ✅ تم فتح دليل Vite
)

if "%web_choice%"=="4" goto :main

goto :success

:success
echo.
echo ✅ تم تنفيذ العملية
echo.
pause
goto :main

:main
cls
goto :start

:start
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                           تشغيل الطوارئ                            ║
echo ║                    عندما لا يعمل أي شيء آخر                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🆘 مرحباً بك في وضع الطوارئ
echo.

echo 🔍 جاري فحص الملفات المتاحة...
echo.

REM فحص العرض التوضيحي
if exist demo.html (
    echo ✅ العرض التوضيحي متوفر
    set demo_available=1
) else (
    echo ❌ العرض التوضيحي غير متوفر
    set demo_available=0
)

REM فحص ملف الاختبار
if exist test.html (
    echo ✅ ملف الاختبار متوفر
    set test_available=1
) else (
    echo ❌ ملف الاختبار غير متوفر
    set test_available=0
)

REM فحص Node.js
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js متوفر
    set nodejs_available=1
) else (
    echo ❌ Node.js غير متوفر
    set nodejs_available=0
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                            خيارات الطوارئ
echo ═══════════════════════════════════════════════════════════════════════
echo.

if %demo_available%==1 (
    echo [1] 🎭 فتح العرض التوضيحي الكامل
)

if %test_available%==1 (
    echo [2] 🧪 فتح صفحة الاختبار
)

if %nodejs_available%==1 (
    echo [3] 🔧 محاولة إصلاح وتشغيل
    echo [4] 🧹 تنظيف شامل وإعادة تثبيت
)

echo [5] 📋 إنشاء عرض بسيط
echo [6] 📖 عرض التعليمات
echo [7] 🌐 فتح مواقع مفيدة
echo [8] ❌ خروج
echo.

set /p choice="اختر الخيار المناسب: "

if "%choice%"=="1" if %demo_available%==1 goto :open_demo
if "%choice%"=="2" if %test_available%==1 goto :open_test
if "%choice%"=="3" if %nodejs_available%==1 goto :try_fix
if "%choice%"=="4" if %nodejs_available%==1 goto :clean_all
if "%choice%"=="5" goto :create_simple
if "%choice%"=="6" goto :show_help
if "%choice%"=="7" goto :open_websites
if "%choice%"=="8" goto :exit

echo ❌ خيار غير متاح أو غير صحيح
timeout /t 2 >nul
goto :main

:exit
echo.
echo 🙏 شكراً لاستخدام نظام العيادات
echo.
timeout /t 2 >nul
exit
