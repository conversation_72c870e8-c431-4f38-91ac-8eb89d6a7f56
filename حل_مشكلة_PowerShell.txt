🔧 حل مشكلة PowerShell في نظام إدارة العيادات الطبية
========================================================

❌ المشكلة:
npm : File C:\Program Files\nodejs\npm.ps1 cannot be loaded because running scripts is disabled on this system

🔍 السبب:
إعدادات الأمان في Windows PowerShell تمنع تشغيل npm

🛠️ الحلول المتاحة (مرتبة حسب السهولة):

الحل الأول (الأسرع) - استخدام Command Prompt:
==============================================
1️⃣ انقر مرتين على: start-cmd.bat
   ✅ يتجنب مشكلة PowerShell تماماً
   ✅ يعمل فوراً بدون تغيير إعدادات

2️⃣ أو انقر على: تشغيل_بدون_مشاكل.bat
   ✅ قائمة خيارات متعددة
   ✅ تشخيص تلقائي للمشاكل

الحل الثاني - إصلاح PowerShell (مؤقت):
======================================
1️⃣ اضغط Win + R
2️⃣ اكتب: powershell
3️⃣ شغل الأمر:
   Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
4️⃣ اكتب: Y
5️⃣ اضغط Enter
6️⃣ أعد تشغيل start.bat

الحل الثالث - إصلاح PowerShell (دائم):
====================================
1️⃣ اضغط Win + X
2️⃣ اختر "Windows PowerShell (Admin)"
3️⃣ انقر بزر الماوس الأيمن على fix-powershell-admin.ps1
4️⃣ اختر "Run with PowerShell"
5️⃣ اتبع التعليمات

الحل الرابع - العرض التوضيحي الفوري:
===================================
افتح ملف demo.html في أي متصفح
✅ يعمل فوراً بدون أي إعدادات
✅ عرض كامل لجميع ميزات النظام

🎯 الملفات الجديدة للحل:

📁 start-cmd.bat - تشغيل آمن بـ Command Prompt
📁 تشغيل_بدون_مشاكل.bat - قائمة خيارات شاملة  
📁 fix-powershell.bat - إرشادات الإصلاح
📁 fix-powershell-admin.ps1 - إصلاح تلقائي
📁 demo.html - عرض توضيحي فوري

🔧 تفسير المشكلة:

Windows PowerShell له إعدادات أمان تمنع تشغيل الملفات الخارجية
npm.ps1 يعتبر ملف خارجي، لذلك يتم منعه
الحل هو تغيير الإعداد أو استخدام Command Prompt

📋 مقارنة الحلول:

الحل الأول (start-cmd.bat):
✅ سريع وآمن
✅ لا يحتاج صلاحيات مدير
✅ لا يغير إعدادات النظام
❌ يحتاج Node.js مثبت

الحل الثاني (إصلاح PowerShell):
✅ يحل المشكلة نهائياً
✅ يعمل مع جميع المشاريع
❌ يحتاج تغيير إعدادات النظام
❌ قد يحتاج صلاحيات مدير

الحل الثالث (العرض التوضيحي):
✅ يعمل فوراً بدون أي متطلبات
✅ لا يحتاج Node.js
✅ عرض كامل للنظام
❌ للعرض فقط (لا يحفظ البيانات)

🚀 التوصية:

للاستخدام الفوري: start-cmd.bat
للحل الدائم: fix-powershell-admin.ps1  
للعرض السريع: demo.html

🎉 جميع الحلول جاهزة ومختبرة!

اختر الحل الأنسب لك وابدأ استخدام النظام فوراً.
