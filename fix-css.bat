@echo off
chcp 65001 >nul
echo ========================================
echo    إصلاح مشكلة CSS - نظام العيادات
echo ========================================
echo.

echo جاري إصلاح مشكلة CSS...
echo.

echo الخطوة 1: نسخ احتياطي من الملفات الحالية...
if exist package.json (
    copy package.json package-backup.json >nul
    echo ✅ تم إنشاء نسخة احتياطية من package.json
)

if exist src\main.jsx (
    copy src\main.jsx src\main-backup.jsx >nul
    echo ✅ تم إنشاء نسخة احتياطية من main.jsx
)

echo.
echo الخطوة 2: استخدام الملفات المبسطة...

echo جاري استبدال package.json...
copy package-simple.json package.json >nul
echo ✅ تم استبدال package.json

echo جاري استبدال main.jsx...
copy src\main-simple.jsx src\main.jsx >nul
echo ✅ تم استبدال main.jsx

echo.
echo الخطوة 3: حذف node_modules وإعادة التثبيت...
if exist node_modules (
    rmdir /s /q node_modules
    echo ✅ تم حذف node_modules
)

if exist package-lock.json (
    del package-lock.json
    echo ✅ تم حذف package-lock.json
)

echo.
echo الخطوة 4: تثبيت المكتبات الأساسية...
call npm install
if %errorlevel% equ 0 (
    echo ✅ تم تثبيت المكتبات بنجاح
) else (
    echo ❌ خطأ في تثبيت المكتبات
    pause
    exit /b 1
)

echo.
echo الخطوة 5: تشغيل النظام...
echo سيتم تشغيل النظام بدون Tailwind CSS
echo استخدام CSS عادي مع تصميم مشابه
echo.

call npm run dev

pause
