# إصلاح مشكلة PowerShell - نظام إدارة العيادات الطبية
# يجب تشغيل هذا الملف كمدير (Run as Administrator)

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "   إصلاح إعدادات PowerShell" -ForegroundColor Cyan
Write-Host "   نظام إدارة العيادات الطبية" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

Write-Host "التحقق من الصلاحيات الحالية..." -ForegroundColor Yellow
$currentPolicy = Get-ExecutionPolicy
Write-Host "الإعداد الحالي: $currentPolicy" -ForegroundColor White

if ($currentPolicy -eq "Restricted") {
    Write-Host ""
    Write-Host "المشكلة: الإعداد الحالي يمنع تشغيل npm" -ForegroundColor Red
    Write-Host "الحل: تغيير الإعداد إلى RemoteSigned" -ForegroundColor Green
    Write-Host ""
    
    try {
        Write-Host "جاري تغيير إعدادات PowerShell..." -ForegroundColor Yellow
        Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser -Force
        Write-Host "✅ تم تغيير الإعدادات بنجاح" -ForegroundColor Green
        Write-Host ""
        Write-Host "الإعداد الجديد: RemoteSigned" -ForegroundColor Green
        Write-Host ""
        Write-Host "يمكنك الآن تشغيل start.bat بدون مشاكل" -ForegroundColor Cyan
    }
    catch {
        Write-Host "❌ خطأ في تغيير الإعدادات" -ForegroundColor Red
        Write-Host "تأكد من تشغيل PowerShell كمدير" -ForegroundColor Yellow
        Write-Host ""
        Write-Host "الحلول البديلة:" -ForegroundColor Cyan
        Write-Host "1. استخدم start-cmd.bat بدلاً من start.bat" -ForegroundColor White
        Write-Host "2. افتح demo.html للعرض التوضيحي" -ForegroundColor White
    }
}
else {
    Write-Host "✅ إعدادات PowerShell صحيحة" -ForegroundColor Green
    Write-Host "يمكنك تشغيل npm بدون مشاكل" -ForegroundColor Green
}

Write-Host ""
Write-Host "اضغط أي مفتاح للمتابعة..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
