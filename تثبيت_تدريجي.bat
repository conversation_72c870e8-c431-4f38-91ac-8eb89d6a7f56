@echo off
chcp 65001 >nul
color 0A
title تثبيت تدريجي - نظام العيادات

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                           تثبيت تدريجي                            ║
echo ║                  تثبيت المكتبات خطوة بخطوة                       ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📦 سيتم تثبيت المكتبات خطوة بخطوة لتجنب الأخطاء
echo.

echo 🔍 فحص المتطلبات...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo يرجى تثبيت Node.js أولاً من: https://nodejs.org
    pause
    exit
)

echo ✅ Node.js متوفر
echo.

echo 🧹 تنظيف أولي...
if exist node_modules (
    echo حذف node_modules...
    rmdir /s /q node_modules
)
if exist package-lock.json (
    echo حذف package-lock.json...
    del package-lock.json
)

echo.
echo 📋 استخدام package.json مبسط...
copy package-minimal.json package.json
echo ✅ تم نسخ package.json المبسط

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                          بدء التثبيت التدريجي
echo ═══════════════════════════════════════════════════════════════════════

echo.
echo الخطوة 1/5: تثبيت React الأساسي...
npm install react react-dom --save
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت React
    goto :error
)
echo ✅ تم تثبيت React

echo.
echo الخطوة 2/5: تثبيت Vite...
npm install vite --save-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت Vite
    goto :error
)
echo ✅ تم تثبيت Vite

echo.
echo الخطوة 3/5: تثبيت plugin React لـ Vite...
npm install @vitejs/plugin-react --save-dev
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت plugin React
    goto :error
)
echo ✅ تم تثبيت plugin React

echo.
echo الخطوة 4/5: تثبيت React Router...
npm install react-router-dom --save
if %errorlevel% neq 0 (
    echo ❌ فشل في تثبيت React Router
    goto :error
)
echo ✅ تم تثبيت React Router

echo.
echo الخطوة 5/5: تثبيت المكتبات الإضافية (اختياري)...
set /p install_extra="هل تريد تثبيت المكتبات الإضافية؟ (y/n): "
if /i "%install_extra%"=="y" (
    echo تثبيت lucide-react...
    npm install lucide-react --save
    
    echo تثبيت react-hot-toast...
    npm install react-hot-toast --save
    
    echo تثبيت date-fns...
    npm install date-fns --save
    
    echo ✅ تم تثبيت المكتبات الإضافية
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                            اكتمل التثبيت!
echo ═══════════════════════════════════════════════════════════════════════

echo.
echo ✅ تم تثبيت جميع المكتبات الأساسية بنجاح!
echo.

echo 📋 المكتبات المثبتة:
npm list --depth=0

echo.
echo 🚀 هل تريد تشغيل النظام الآن؟ (y/n): 
set /p run_now=""
if /i "%run_now%"=="y" (
    echo.
    echo جاري تشغيل النظام...
    npm run dev
) else (
    echo.
    echo يمكنك تشغيل النظام لاحقاً باستخدام: npm run dev
)

goto :end

:error
echo.
echo ❌ حدث خطأ في التثبيت
echo.
echo 🔄 الحلول المتاحة:
echo 1. جرب إصلاح_التثبيت.bat
echo 2. تأكد من الاتصال بالإنترنت
echo 3. استخدم العرض التوضيحي: demo.html
echo.

set /p try_demo="هل تريد فتح العرض التوضيحي؟ (y/n): "
if /i "%try_demo%"=="y" (
    if exist demo.html (
        start demo.html
        echo ✅ تم فتح العرض التوضيحي
    ) else (
        echo ❌ العرض التوضيحي غير متوفر
    )
)

:end
echo.
pause
