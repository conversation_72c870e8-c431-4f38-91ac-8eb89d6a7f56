@echo off
chcp 65001 >nul
title نظام إدارة العيادات الطبية - مركز د. حمدي عواد

echo.
echo ========================================
echo    نظام إدارة العيادات الطبية
echo    مركز د. حمدي عواد الطبي
echo ========================================
echo.
echo استخدام Command Prompt لتجنب مشاكل PowerShell
echo.

echo التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo.
    echo يرجى تحميل وتثبيت Node.js من:
    echo https://nodejs.org
    echo.
    echo بعد التثبيت، أعد تشغيل هذا الملف
    echo.
    echo للعرض التوضيحي الآن، افتح ملف demo.html
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
node --version
echo.

echo جاري تثبيت المكتبات...
echo هذا قد يستغرق بضع دقائق في المرة الأولى...
echo.

npm install
if %errorlevel% neq 0 (
    echo.
    echo ❌ خطأ في تثبيت المكتبات
    echo.
    echo الحلول البديلة:
    echo 1. تأكد من اتصال الإنترنت
    echo 2. شغل الأمر: npm cache clean --force
    echo 3. احذف مجلد node_modules وأعد المحاولة
    echo 4. استخدم العرض التوضيحي: demo.html
    echo.
    pause
    exit /b 1
)

echo.
echo ✅ تم تثبيت المكتبات بنجاح
echo.

echo جاري تشغيل النظام...
echo.
echo سيتم فتح النظام في المتصفح على العنوان:
echo http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

npm run dev

echo.
echo تم إيقاف النظام
pause
