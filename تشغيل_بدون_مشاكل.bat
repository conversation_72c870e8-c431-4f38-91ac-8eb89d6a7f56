@echo off
chcp 65001 >nul
color 0A
title نظام إدارة العيادات الطبية - تشغيل آمن

echo.
echo ╔══════════════════════════════════════════════════════════════╗
echo ║                    نظام إدارة العيادات الطبية                    ║
echo ║                    مركز د. حمدي عواد الطبي                     ║
echo ║                        تشغيل آمن                            ║
echo ╚══════════════════════════════════════════════════════════════╝
echo.

echo 🔍 تشخيص المشكلة...
echo.

echo التحقق من Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    echo.
    echo 📥 يرجى تحميل Node.js من: https://nodejs.org
    echo.
    echo 🎯 للعرض الفوري: افتح demo.html في المتصفح
    goto :end
)

echo ✅ Node.js مثبت
node --version
echo.

echo التحقق من npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    goto :alternative
)

echo ✅ npm متاح
echo.

echo 🚀 اختر طريقة التشغيل:
echo.
echo [1] تشغيل عادي (قد يواجه مشكلة PowerShell)
echo [2] تشغيل آمن باستخدام Command Prompt
echo [3] عرض توضيحي فوري (demo.html)
echo [4] إصلاح مشكلة PowerShell
echo [5] خروج
echo.

set /p choice="اختر رقم (1-5): "

if "%choice%"=="1" goto :normal
if "%choice%"=="2" goto :safe
if "%choice%"=="3" goto :demo
if "%choice%"=="4" goto :fix
if "%choice%"=="5" goto :end

echo خيار غير صحيح، سيتم استخدام التشغيل الآمن...
goto :safe

:normal
echo.
echo 🔄 تشغيل عادي...
npm install
if %errorlevel% neq 0 goto :error
npm run dev
goto :end

:safe
echo.
echo 🛡️ تشغيل آمن...
echo جاري فتح نافذة Command Prompt منفصلة...
start cmd /k "title نظام العيادات && echo جاري التثبيت... && npm install && echo جاري التشغيل... && npm run dev"
echo.
echo ✅ تم فتح النظام في نافذة منفصلة
goto :end

:demo
echo.
echo 🎭 فتح العرض التوضيحي...
if exist demo.html (
    start demo.html
    echo ✅ تم فتح العرض التوضيحي في المتصفح
) else (
    echo ❌ ملف demo.html غير موجود
)
goto :end

:fix
echo.
echo 🔧 إصلاح مشكلة PowerShell...
echo.
echo يرجى اتباع الخطوات التالية:
echo.
echo 1️⃣ اضغط Win + X واختر "Windows PowerShell (Admin)"
echo 2️⃣ شغل الأمر التالي:
echo    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
echo 3️⃣ اكتب Y واضغط Enter
echo 4️⃣ أعد تشغيل هذا الملف
echo.
echo أو استخدم الملف: fix-powershell-admin.ps1
echo.
pause
goto :end

:alternative
echo.
echo 🔄 حلول بديلة:
echo.
echo 1️⃣ العرض التوضيحي: demo.html
echo 2️⃣ إعادة تثبيت Node.js
echo 3️⃣ استخدام متصفح للعرض
echo.

:error
echo.
echo ❌ حدث خطأ في التشغيل
echo.
echo 🛠️ الحلول المتاحة:
echo.
echo 1️⃣ استخدم: start-cmd.bat
echo 2️⃣ افتح: demo.html
echo 3️⃣ أعد تثبيت Node.js
echo.

:end
echo.
echo شكراً لاستخدام نظام إدارة العيادات الطبية
pause
