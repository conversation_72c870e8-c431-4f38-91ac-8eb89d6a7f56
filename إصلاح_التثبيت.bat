@echo off
chcp 65001 >nul
color 0E
title إصلاح مشاكل التثبيت - نظام العيادات

cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                          إصلاح مشاكل التثبيت                          ║
echo ║                    نظام إدارة العيادات الطبية                     ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🔧 مرحباً بك في أداة إصلاح مشاكل التثبيت
echo.

echo 🔍 جاري تشخيص المشكلة...
echo.

REM فحص Node.js
echo التحقق من Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    goto :no_nodejs
) else (
    echo ✅ Node.js مثبت
    node --version
)

REM فحص npm
echo التحقق من npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    goto :no_npm
) else (
    echo ✅ npm متاح
    npm --version
)

REM فحص الاتصال بالإنترنت
echo التحقق من الاتصال بالإنترنت...
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لا يوجد اتصال بالإنترنت
    goto :no_internet
) else (
    echo ✅ الاتصال بالإنترنت متاح
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                            حلول مشاكل التثبيت
echo ═══════════════════════════════════════════════════════════════════════
echo.

echo [1] 🧹 تنظيف شامل وإعادة تثبيت
echo [2] 🔄 تغيير مصدر npm (registry)
echo [3] 📦 تثبيت المكتبات الأساسية فقط
echo [4] 🌐 تثبيت بدون SSL
echo [5] 🔧 إصلاح صلاحيات npm
echo [6] 💾 تثبيت محلي (offline)
echo [7] 🎭 استخدام العرض التوضيحي
echo [8] 📋 عرض تفاصيل الخطأ
echo [9] ❌ خروج
echo.

set /p choice="اختر الحل المناسب (1-9): "

if "%choice%"=="1" goto :clean_install
if "%choice%"=="2" goto :change_registry
if "%choice%"=="3" goto :minimal_install
if "%choice%"=="4" goto :no_ssl_install
if "%choice%"=="5" goto :fix_permissions
if "%choice%"=="6" goto :offline_install
if "%choice%"=="7" goto :use_demo
if "%choice%"=="8" goto :show_error_details
if "%choice%"=="9" goto :exit

echo ❌ خيار غير صحيح
timeout /t 2 >nul
goto :main

:clean_install
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                         تنظيف شامل وإعادة تثبيت                        ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🧹 جاري التنظيف الشامل...
echo.

echo الخطوة 1: حذف node_modules
if exist node_modules (
    echo جاري حذف node_modules...
    rmdir /s /q node_modules
    echo ✅ تم حذف node_modules
) else (
    echo ✅ node_modules غير موجود
)

echo.
echo الخطوة 2: حذف package-lock.json
if exist package-lock.json (
    del package-lock.json
    echo ✅ تم حذف package-lock.json
) else (
    echo ✅ package-lock.json غير موجود
)

echo.
echo الخطوة 3: تنظيف cache npm
echo جاري تنظيف cache npm...
npm cache clean --force
echo ✅ تم تنظيف cache npm

echo.
echo الخطوة 4: إعادة التثبيت
echo جاري إعادة تثبيت المكتبات...
echo هذا قد يستغرق عدة دقائق...
echo.

npm install --verbose
if %errorlevel% equ 0 (
    echo.
    echo ✅ تم التثبيت بنجاح!
    echo جاري تشغيل النظام...
    npm run dev
) else (
    echo.
    echo ❌ فشل التثبيت مرة أخرى
    echo جرب حل آخر من القائمة
)

pause
goto :main

:change_registry
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                        تغيير مصدر npm                              ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🔄 جاري تغيير مصدر npm...
echo.

echo المصدر الحالي:
npm config get registry

echo.
echo [1] استخدام مصدر Taobao (الصين - سريع)
echo [2] استخدام مصدر npmjs الرسمي
echo [3] استخدام مصدر Yarn
echo [4] العودة
echo.

set /p reg_choice="اختر المصدر (1-4): "

if "%reg_choice%"=="1" (
    echo جاري تعيين مصدر Taobao...
    npm config set registry https://registry.npmmirror.com/
    echo ✅ تم تعيين مصدر Taobao
)

if "%reg_choice%"=="2" (
    echo جاري تعيين المصدر الرسمي...
    npm config set registry https://registry.npmjs.org/
    echo ✅ تم تعيين المصدر الرسمي
)

if "%reg_choice%"=="3" (
    echo جاري تعيين مصدر Yarn...
    npm config set registry https://registry.yarnpkg.com/
    echo ✅ تم تعيين مصدر Yarn
)

if "%reg_choice%"=="4" goto :main

echo.
echo جاري إعادة المحاولة مع المصدر الجديد...
npm install
if %errorlevel% equ 0 (
    echo ✅ نجح التثبيت مع المصدر الجديد!
    npm run dev
) else (
    echo ❌ لا يزال هناك خطأ
)

pause
goto :main

:minimal_install
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                      تثبيت المكتبات الأساسية فقط                      ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📦 جاري تثبيت المكتبات الأساسية فقط...
echo.

echo تنظيف أولي...
if exist node_modules rmdir /s /q node_modules
if exist package-lock.json del package-lock.json

echo.
echo تثبيت React الأساسي...
npm install react react-dom
if %errorlevel% neq 0 goto :minimal_failed

echo.
echo تثبيت Vite...
npm install vite @vitejs/plugin-react
if %errorlevel% neq 0 goto :minimal_failed

echo.
echo تثبيت React Router...
npm install react-router-dom
if %errorlevel% neq 0 goto :minimal_failed

echo.
echo ✅ تم تثبيت المكتبات الأساسية بنجاح!
echo جاري تشغيل النظام...
npm run dev
goto :end

:minimal_failed
echo ❌ فشل في تثبيت المكتبات الأساسية
echo جرب حل آخر
pause
goto :main

:no_ssl_install
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                        تثبيت بدون SSL                              ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🌐 جاري التثبيت بدون SSL (غير آمن مؤقتاً)...
echo.

echo تعيين إعدادات npm...
npm config set strict-ssl false
npm config set registry http://registry.npmjs.org/

echo.
echo جاري التثبيت...
npm install --no-optional
if %errorlevel% equ 0 (
    echo ✅ نجح التثبيت!
    echo إعادة تعيين الإعدادات الآمنة...
    npm config set strict-ssl true
    npm config set registry https://registry.npmjs.org/
    npm run dev
) else (
    echo ❌ فشل التثبيت
    npm config set strict-ssl true
    npm config set registry https://registry.npmjs.org/
)

pause
goto :main

:fix_permissions
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                         إصلاح صلاحيات npm                          ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🔧 جاري إصلاح صلاحيات npm...
echo.

echo تنظيف cache npm...
npm cache clean --force

echo.
echo إعادة تعيين إعدادات npm...
npm config delete prefix
npm config delete cache

echo.
echo تحديث npm...
npm install -g npm@latest
if %errorlevel% neq 0 (
    echo تعذر تحديث npm، المتابعة بالإصدار الحالي...
)

echo.
echo جاري إعادة المحاولة...
npm install
if %errorlevel% equ 0 (
    echo ✅ تم إصلاح المشكلة!
    npm run dev
) else (
    echo ❌ لا تزال المشكلة موجودة
)

pause
goto :main

:offline_install
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                          تثبيت محلي                               ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 💾 محاولة التثبيت المحلي...
echo.

echo إنشاء package.json مبسط...
echo { > package-simple.json
echo   "name": "clinic-management", >> package-simple.json
echo   "version": "1.0.0", >> package-simple.json
echo   "scripts": { >> package-simple.json
echo     "dev": "echo 'استخدم demo.html للعرض'" >> package-simple.json
echo   } >> package-simple.json
echo } >> package-simple.json

copy package-simple.json package.json

echo.
echo ✅ تم إنشاء إعداد محلي مبسط
echo 🎭 يرجى استخدام demo.html للعرض التوضيحي

if exist demo.html (
    start demo.html
    echo ✅ تم فتح العرض التوضيحي
) else (
    echo ❌ العرض التوضيحي غير متوفر
)

pause
goto :main

:use_demo
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                        استخدام العرض التوضيحي                        ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🎭 جاري فتح العرض التوضيحي...
echo.

if exist demo.html (
    start demo.html
    echo ✅ تم فتح العرض التوضيحي في المتصفح
    echo.
    echo 📋 مميزات العرض التوضيحي:
    echo • واجهة كاملة للنظام
    echo • تصميم احترافي
    echo • لا يحتاج تثبيت مكتبات
    echo • يعمل في أي متصفح
    echo.
    echo هذا حل مؤقت حتى يتم حل مشكلة التثبيت
) else (
    echo ❌ ملف العرض التوضيحي غير موجود
    echo جاري إنشاء عرض بسيط...
    call :create_simple_demo
)

pause
goto :main

:show_error_details
cls
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                         تفاصيل الأخطاء الشائعة                        ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 📋 الأخطاء الشائعة وحلولها:
echo.

echo ❌ "ENOTFOUND registry.npmjs.org"
echo    الحل: مشكلة في الاتصال بالإنترنت أو DNS
echo    جرب: تغيير مصدر npm (الخيار 2)
echo.

echo ❌ "EACCES: permission denied"
echo    الحل: مشكلة في الصلاحيات
echo    جرب: إصلاح صلاحيات npm (الخيار 5)
echo.

echo ❌ "CERT_UNTRUSTED"
echo    الحل: مشكلة في شهادات SSL
echo    جرب: تثبيت بدون SSL (الخيار 4)
echo.

echo ❌ "ERESOLVE unable to resolve dependency tree"
echo    الحل: تضارب في إصدارات المكتبات
echo    جرب: تنظيف شامل (الخيار 1)
echo.

echo ❌ "npm ERR! code ETIMEDOUT"
echo    الحل: انتهت مهلة الاتصال
echo    جرب: تغيير مصدر npm (الخيار 2)
echo.

echo ❌ "gyp ERR! stack Error: not found: python"
echo    الحل: Python غير مثبت (مطلوب لبعض المكتبات)
echo    جرب: تثبيت المكتبات الأساسية فقط (الخيار 3)
echo.

pause
goto :main

:no_nodejs
echo.
echo ❌ Node.js غير مثبت
echo.
echo 📥 يرجى تحميل وتثبيت Node.js من:
echo 🌐 https://nodejs.org
echo.
echo 📋 خطوات التثبيت:
echo 1. اذهب إلى nodejs.org
echo 2. حمل النسخة LTS (الموصى بها)
echo 3. شغل ملف التثبيت
echo 4. أعد تشغيل الكمبيوتر
echo 5. أعد تشغيل هذا الملف
echo.

set /p open_site="هل تريد فتح موقع Node.js؟ (y/n): "
if /i "%open_site%"=="y" start https://nodejs.org

echo.
echo 🎭 للعرض الفوري: استخدم demo.html
if exist demo.html (
    set /p open_demo="هل تريد فتح العرض التوضيحي؟ (y/n): "
    if /i "%open_demo%"=="y" start demo.html
)

pause
goto :main

:no_npm
echo.
echo ❌ npm غير متاح
echo هذا يعني أن Node.js لم يتم تثبيته بشكل صحيح
echo.
echo 🔄 الحلول:
echo 1. أعد تثبيت Node.js
echo 2. أعد تشغيل الكمبيوتر
echo 3. تأكد من إضافة Node.js إلى PATH
echo.
pause
goto :main

:no_internet
echo.
echo ❌ لا يوجد اتصال بالإنترنت
echo.
echo 🌐 npm يحتاج اتصال بالإنترنت لتحميل المكتبات
echo.
echo 🔄 الحلول:
echo 1. تأكد من الاتصال بالإنترنت
echo 2. تحقق من إعدادات Firewall
echo 3. جرب شبكة أخرى
echo 4. استخدم العرض التوضيحي (demo.html)
echo.

if exist demo.html (
    set /p open_demo="هل تريد فتح العرض التوضيحي؟ (y/n): "
    if /i "%open_demo%"=="y" start demo.html
)

pause
goto :main

:create_simple_demo
echo جاري إنشاء عرض بسيط...
echo ^<!DOCTYPE html^> > عرض_بسيط.html
echo ^<html lang="ar" dir="rtl"^> >> عرض_بسيط.html
echo ^<head^>^<meta charset="UTF-8"^>^<title^>نظام العيادات^</title^> >> عرض_بسيط.html
echo ^<style^>body{font-family:Arial;text-align:center;padding:50px;background:linear-gradient(135deg,#667eea,#764ba2);color:white}^</style^> >> عرض_بسيط.html
echo ^</head^>^<body^> >> عرض_بسيط.html
echo ^<h1^>🏥 نظام إدارة العيادات الطبية^</h1^> >> عرض_بسيط.html
echo ^<h2^>مركز د. حمدي عواد الطبي^</h2^> >> عرض_بسيط.html
echo ^<p^>عذراً، حدث خطأ في التثبيت^</p^> >> عرض_بسيط.html
echo ^<p^>هذا عرض مؤقت حتى يتم حل المشكلة^</p^> >> عرض_بسيط.html
echo ^</body^>^</html^> >> عرض_بسيط.html
start عرض_بسيط.html
exit /b 0

:main
cls
goto :start

:start
echo.
echo ╔══════════════════════════════════════════════════════════════════════╗
echo ║                          إصلاح مشاكل التثبيت                          ║
echo ║                    نظام إدارة العيادات الطبية                     ║
echo ╚══════════════════════════════════════════════════════════════════════╝
echo.

echo 🔧 مرحباً بك في أداة إصلاح مشاكل التثبيت
echo.

echo 🔍 جاري تشخيص المشكلة...
echo.

REM فحص Node.js
echo التحقق من Node.js...
where node >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js غير مثبت
    goto :no_nodejs
) else (
    echo ✅ Node.js مثبت
    node --version
)

REM فحص npm
echo التحقق من npm...
where npm >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ npm غير متاح
    goto :no_npm
) else (
    echo ✅ npm متاح
    npm --version
)

REM فحص الاتصال بالإنترنت
echo التحقق من الاتصال بالإنترنت...
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ لا يوجد اتصال بالإنترنت
    goto :no_internet
) else (
    echo ✅ الاتصال بالإنترنت متاح
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                            حلول مشاكل التثبيت
echo ═══════════════════════════════════════════════════════════════════════
echo.

echo [1] 🧹 تنظيف شامل وإعادة تثبيت
echo [2] 🔄 تغيير مصدر npm (registry)
echo [3] 📦 تثبيت المكتبات الأساسية فقط
echo [4] 🌐 تثبيت بدون SSL
echo [5] 🔧 إصلاح صلاحيات npm
echo [6] 💾 تثبيت محلي (offline)
echo [7] 🎭 استخدام العرض التوضيحي
echo [8] 📋 عرض تفاصيل الخطأ
echo [9] ❌ خروج
echo.

set /p choice="اختر الحل المناسب (1-9): "

if "%choice%"=="1" goto :clean_install
if "%choice%"=="2" goto :change_registry
if "%choice%"=="3" goto :minimal_install
if "%choice%"=="4" goto :no_ssl_install
if "%choice%"=="5" goto :fix_permissions
if "%choice%"=="6" goto :offline_install
if "%choice%"=="7" goto :use_demo
if "%choice%"=="8" goto :show_error_details
if "%choice%"=="9" goto :exit

echo ❌ خيار غير صحيح
timeout /t 2 >nul
goto :main

:end
pause
goto :main

:exit
echo.
echo 🙏 شكراً لاستخدام أداة إصلاح التثبيت
echo.
timeout /t 2 >nul
exit
