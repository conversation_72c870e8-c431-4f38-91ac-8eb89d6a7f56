🏥 نظام إدارة العيادات الطبية - مركز د. حمدي عواد
=====================================================

📋 ملفات مهمة للتشغيل:
===================

1️⃣ للعرض السريع (بدون تثبيت):
   📄 demo.html - افتح هذا الملف في المتصفح للعرض التوضيحي

2️⃣ للنظام الكامل:
   🔧 start.bat - انقر مرتين لتشغيل النظام (Windows)
   🔧 start.sh - للأنظمة الأخرى (Mac/Linux)

3️⃣ ملفات التعليمات:
   📖 README.md - دليل شامل للمشروع
   ⚡ QUICK_START.md - دليل التشغيل السريع
   🧪 test.html - صفحة اختبار النظام

🚀 خطوات التشغيل السريع:
========================

الطريقة الأولى (الأسهل):
------------------------
1. انقر مرتين على ملف start.bat
2. انتظر حتى يكتمل التثبيت
3. سيفتح النظام تلقائياً في المتصفح

الطريقة الثانية (يدوياً):
-------------------------
1. تأكد من تثبيت Node.js من https://nodejs.org
2. افتح Terminal في مجلد المشروع
3. شغل: npm install
4. شغل: npm run dev
5. افتح http://localhost:3000

الطريقة الثالثة (للعرض فقط):
-----------------------------
افتح ملف demo.html في أي متصفح

⚠️ حل المشاكل الشائعة:
=====================

❌ "npm غير معروف":
   ➡️ تأكد من تثبيت Node.js أولاً

❌ "خطأ في التثبيت":
   ➡️ احذف مجلد node_modules وشغل npm install مرة أخرى

❌ "الصفحة لا تعمل":
   ➡️ تأكد من أن المنفذ 3000 غير مستخدم

🎯 مميزات النظام:
================

✅ سلة الخدمات الاحترافية (3 أعمدة)
✅ إدارة المرضى (40+ مريض)
✅ إدارة الأطباء (50+ طبيب)
✅ إدارة الموظفين والأقسام
✅ نظام الحجوزات والبصمة
✅ التقارير المالية الشاملة
✅ التكامل مع Google Sheets
✅ تصميم عربي احترافي
✅ أكثر من 150 خدمة طبية

📞 للدعم:
==========
راجع ملف README.md للتفاصيل الكاملة

🎉 مبروك! النظام جاهز للاستخدام
