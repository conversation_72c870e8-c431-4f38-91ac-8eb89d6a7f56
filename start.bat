@echo off
chcp 65001 >nul
echo ========================================
echo    نظام إدارة العيادات الطبية
echo    مركز د. حمدي عواد الطبي
echo ========================================
echo.

echo التحقق من Node.js...
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ خطأ: Node.js غير مثبت!
    echo يرجى تحميل وتثبيت Node.js من: https://nodejs.org
    echo.
    echo للعرض التوضيحي، افتح ملف demo.html في المتصفح
    pause
    exit /b 1
)

echo ✅ Node.js مثبت بنجاح
echo.

echo جاري تثبيت المكتبات...
call npm install
if %errorlevel% neq 0 (
    echo ❌ خطأ في تثبيت المكتبات
    pause
    exit /b 1
)

echo ✅ تم تثبيت المكتبات بنجاح
echo.

echo جاري تشغيل النظام...
echo سيتم فتح النظام في المتصفح على العنوان:
echo http://localhost:3000
echo.
echo للإيقاف: اضغط Ctrl+C
echo.

call npm run dev

pause
