@echo off
chcp 65001 >nul
echo ========================================
echo    إصلاح مشكلة PowerShell - نظام العيادات
echo ========================================
echo.

echo المشكلة: تم منع تشغيل npm بسبب إعدادات الأمان
echo الحل: تغيير إعدادات PowerShell مؤقتاً
echo.

echo الطريقة 1: استخدام Command Prompt بدلاً من PowerShell
echo ========================================================
echo.

echo جاري فتح Command Prompt...
echo سيتم تشغيل الأوامر في نافذة منفصلة
echo.

start cmd /k "echo نظام إدارة العيادات الطبية && echo. && echo جاري تثبيت المكتبات... && npm install && echo. && echo جاري تشغيل النظام... && npm run dev"

echo.
echo إذا لم تعمل الطريقة الأولى، اتبع الخطوات التالية:
echo.
echo الطريقة 2: تغيير إعدادات PowerShell
echo =====================================
echo.
echo 1. افتح PowerShell كمدير (Run as Administrator)
echo 2. شغل الأمر التالي:
echo    Set-ExecutionPolicy -ExecutionPolicy RemoteSigned -Scope CurrentUser
echo 3. اكتب Y واضغط Enter
echo 4. أغلق PowerShell وأعد تشغيل start.bat
echo.

echo الطريقة 3: استخدام العرض التوضيحي
echo ==================================
echo افتح ملف demo.html في المتصفح للحصول على عرض كامل
echo.

pause
