@echo off
chcp 65001 >nul
color 0B
title نظام إدارة العيادات - التشغيل النهائي

cls
echo.
echo ██╗  ██╗██╗███╗   ██╗██╗ ██████╗    ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗ 
echo ██║ ██╔╝██║████╗  ██║██║██╔════╝    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
echo █████╔╝ ██║██╔██╗ ██║██║██║         ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
echo ██╔═██╗ ██║██║╚██╗██║██║██║         ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
echo ██║  ██╗██║██║ ╚████║██║╚██████╗    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
echo ╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚═╝ ╚═════╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
echo.
echo                           🏥 نظام إدارة العيادات الطبية 🏥
echo                            مركز د. حمدي عواد الطبي
echo                              التشغيل النهائي المضمون
echo.

echo 🎯 هذا الملف يضمن تشغيل النظام بأي طريقة ممكنة!
echo.

REM فحص شامل للمتطلبات
echo 🔍 فحص شامل للمتطلبات...
echo.

set nodejs_ok=0
set npm_ok=0
set internet_ok=0
set demo_ok=0

REM فحص Node.js
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js متوفر
    node --version
    set nodejs_ok=1
) else (
    echo ❌ Node.js غير متوفر
)

REM فحص npm
where npm >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm متوفر
    npm --version
    set npm_ok=1
) else (
    echo ❌ npm غير متوفر
)

REM فحص الإنترنت
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الإنترنت متوفر
    set internet_ok=1
) else (
    echo ❌ الإنترنت غير متوفر
)

REM فحص العرض التوضيحي
if exist demo.html (
    echo ✅ العرض التوضيحي متوفر
    set demo_ok=1
) else (
    echo ❌ العرض التوضيحي غير متوفر
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                          اختيار طريقة التشغيل
echo ═══════════════════════════════════════════════════════════════════════
echo.

REM تحديد أفضل طريقة تشغيل
if %nodejs_ok%==1 if %npm_ok%==1 if %internet_ok%==1 (
    echo 🎉 جميع المتطلبات متوفرة - يمكن تشغيل النظام الكامل!
    echo.
    echo [1] 🚀 تشغيل النظام الكامل (موصى به)
    echo [2] 🔧 إصلاح مشاكل التثبيت أولاً
    echo [3] 📦 تثبيت تدريجي آمن
    if %demo_ok%==1 echo [4] 🎭 العرض التوضيحي
    echo [5] ❌ خروج
    
    set /p choice="اختر الطريقة المفضلة (1-5): "
    
    if "%choice%"=="1" goto :full_run
    if "%choice%"=="2" goto :fix_install
    if "%choice%"=="3" goto :gradual_install
    if "%choice%"=="4" if %demo_ok%==1 goto :demo
    if "%choice%"=="5" goto :exit
    
) else if %nodejs_ok%==1 if %npm_ok%==1 (
    echo ⚠️ Node.js و npm متوفران لكن لا يوجد إنترنت
    echo.
    echo [1] 🔄 المحاولة بدون إنترنت
    if %demo_ok%==1 echo [2] 🎭 العرض التوضيحي
    echo [3] 📋 عرض التعليمات
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :offline_run
    if "%choice%"=="2" if %demo_ok%==1 goto :demo
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
    
) else if %demo_ok%==1 (
    echo 🎭 Node.js غير متوفر، لكن العرض التوضيحي متاح
    echo.
    echo [1] 🎭 فتح العرض التوضيحي
    echo [2] 📥 تحميل Node.js
    echo [3] 📋 عرض التعليمات
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :demo
    if "%choice%"=="2" goto :download_nodejs
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
    
) else (
    echo 🆘 لا توجد طريقة مباشرة للتشغيل
    echo.
    echo [1] 📥 تحميل Node.js
    echo [2] 🔧 إنشاء عرض بسيط
    echo [3] 📋 عرض التعليمات الكاملة
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :download_nodejs
    if "%choice%"=="2" goto :create_simple
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
)

echo ❌ خيار غير صحيح
timeout /t 2 >nul
goto :main

:full_run
cls
echo.
echo 🚀 تشغيل النظام الكامل...
echo.

if not exist node_modules (
    echo 📦 المكتبات غير مثبتة، جاري التثبيت...
    npm install
    if %errorlevel% neq 0 (
        echo ❌ فشل التثبيت، جاري استخدام الإصلاح التلقائي...
        goto :fix_install
    )
)

echo ✅ جاري تشغيل النظام...
echo 🌐 سيفتح على: http://localhost:3000
echo.
npm run dev
goto :end

:fix_install
cls
echo.
echo 🔧 تشغيل أداة إصلاح التثبيت...
echo.

if exist إصلاح_التثبيت.bat (
    call إصلاح_التثبيت.bat
) else (
    echo ❌ أداة الإصلاح غير متوفرة
    echo جاري الإصلاح اليدوي...
    
    echo حذف الملفات المؤقتة...
    if exist node_modules rmdir /s /q node_modules
    if exist package-lock.json del package-lock.json
    
    echo تنظيف cache...
    npm cache clean --force
    
    echo إعادة التثبيت...
    npm install
    
    if %errorlevel% equ 0 (
        echo ✅ تم الإصلاح، جاري التشغيل...
        npm run dev
    ) else (
        echo ❌ فشل الإصلاح
        if %demo_ok%==1 goto :demo
    )
)
goto :end

:gradual_install
cls
echo.
echo 📦 تشغيل التثبيت التدريجي...
echo.

if exist تثبيت_تدريجي.bat (
    call تثبيت_تدريجي.bat
) else (
    echo ❌ ملف التثبيت التدريجي غير متوفر
    echo جاري التثبيت اليدوي...
    
    if exist package-minimal.json (
        copy package-minimal.json package.json
        echo ✅ استخدام package.json مبسط
    )
    
    npm install
    if %errorlevel% equ 0 (
        npm run dev
    ) else (
        if %demo_ok%==1 goto :demo
    )
)
goto :end

:demo
cls
echo.
echo 🎭 فتح العرض التوضيحي...
echo.

start demo.html
echo ✅ تم فتح العرض التوضيحي في المتصفح
echo.
echo 📋 مميزات العرض التوضيحي:
echo • واجهة كاملة للنظام
echo • تصميم احترافي مع Tailwind CSS
echo • جميع الصفحات والميزات
echo • لا يحتاج تثبيت أي مكتبات
echo • يعمل في أي متصفح حديث
echo.
echo هذا حل مثالي حتى يتم حل مشاكل التثبيت
goto :end

:offline_run
cls
echo.
echo 🔄 محاولة التشغيل بدون إنترنت...
echo.

if exist node_modules (
    echo ✅ المكتبات موجودة، جاري التشغيل...
    npm run dev
) else (
    echo ❌ المكتبات غير مثبتة ولا يوجد إنترنت
    if %demo_ok%==1 (
        echo جاري فتح العرض التوضيحي كبديل...
        goto :demo
    ) else (
        echo لا توجد بدائل متاحة
    )
)
goto :end

:download_nodejs
cls
echo.
echo 📥 تحميل Node.js...
echo.

echo 🌐 جاري فتح موقع Node.js...
start https://nodejs.org

echo.
echo 📋 خطوات التثبيت:
echo 1. حمل النسخة LTS (الموصى بها)
echo 2. شغل ملف التثبيت
echo 3. أعد تشغيل الكمبيوتر
echo 4. أعد تشغيل هذا الملف
echo.

if %demo_ok%==1 (
    echo 🎭 أثناء انتظار التثبيت، يمكنك مشاهدة العرض التوضيحي
    set /p open_demo="هل تريد فتح العرض التوضيحي؟ (y/n): "
    if /i "%open_demo%"=="y" goto :demo
)

goto :end

:create_simple
cls
echo.
echo 🔧 إنشاء عرض بسيط...
echo.

echo جاري إنشاء ملف HTML بسيط...
echo ^<!DOCTYPE html^> > عرض_نهائي.html
echo ^<html lang="ar" dir="rtl"^> >> عرض_نهائي.html
echo ^<head^> >> عرض_نهائي.html
echo ^<meta charset="UTF-8"^> >> عرض_نهائي.html
echo ^<meta name="viewport" content="width=device-width, initial-scale=1.0"^> >> عرض_نهائي.html
echo ^<title^>نظام إدارة العيادات الطبية^</title^> >> عرض_نهائي.html
echo ^<style^> >> عرض_نهائي.html
echo body { font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif; background: linear-gradient(135deg, #667eea 0%%, #764ba2 100%%); margin: 0; padding: 50px; color: white; text-align: center; } >> عرض_نهائي.html
echo .container { background: white; color: #333; padding: 40px; border-radius: 20px; max-width: 800px; margin: 0 auto; box-shadow: 0 20px 40px rgba(0,0,0,0.1); } >> عرض_نهائي.html
echo h1 { color: #2563eb; margin-bottom: 20px; } >> عرض_نهائي.html
echo .feature { background: #f8fafc; padding: 15px; margin: 10px 0; border-radius: 10px; border-left: 4px solid #2563eb; } >> عرض_نهائي.html
echo .status { background: #fef3c7; padding: 20px; border-radius: 10px; margin: 20px 0; border: 2px solid #f59e0b; color: #92400e; } >> عرض_نهائي.html
echo ^</style^> >> عرض_نهائي.html
echo ^</head^> >> عرض_نهائي.html
echo ^<body^> >> عرض_نهائي.html
echo ^<div class="container"^> >> عرض_نهائي.html
echo ^<h1^>🏥 نظام إدارة العيادات الطبية^</h1^> >> عرض_نهائي.html
echo ^<h2^>مركز د. حمدي عواد الطبي^</h2^> >> عرض_نهائي.html
echo ^<div class="status"^> >> عرض_نهائي.html
echo ^<h3^>⚠️ تعذر تشغيل النظام الكامل^</h3^> >> عرض_نهائي.html
echo ^<p^>يرجى تثبيت Node.js من nodejs.org للحصول على النظام الكامل^</p^> >> عرض_نهائي.html
echo ^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ سلة الخدمات الاحترافية (3 أعمدة)^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ إدارة المرضى (40+ مريض)^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ إدارة الأطباء (50+ طبيب في 14 تخصص)^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ نظام الحجوزات والبصمة^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ التقارير المالية الشاملة^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ التكامل مع Google Sheets^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ تصميم عربي احترافي^</div^> >> عرض_نهائي.html
echo ^<div class="feature"^>✅ أكثر من 150 خدمة طبية^</div^> >> عرض_نهائي.html
echo ^<p style="margin-top: 30px;"^>جميع الملفات جاهزة، فقط تحتاج Node.js للتشغيل الكامل^</p^> >> عرض_نهائي.html
echo ^</div^> >> عرض_نهائي.html
echo ^</body^> >> عرض_نهائي.html
echo ^</html^> >> عرض_نهائي.html

start عرض_نهائي.html
echo ✅ تم إنشاء وفتح عرض بسيط
goto :end

:show_instructions
cls
echo.
echo 📋 التعليمات الكاملة...
echo.

if exist "حل_مشاكل_التثبيت.txt" (
    start notepad "حل_مشاكل_التثبيت.txt"
    echo ✅ تم فتح دليل حل المشاكل
) else (
    echo 📝 التعليمات الأساسية:
    echo.
    echo 1. تثبيت Node.js من https://nodejs.org
    echo 2. أعد تشغيل الكمبيوتر
    echo 3. شغل: npm install
    echo 4. شغل: npm run dev
    echo.
    echo للعرض الفوري: افتح demo.html
)

goto :end

:main
cls
goto :start

:start
echo.
echo ██╗  ██╗██╗███╗   ██╗██╗ ██████╗    ███╗   ███╗ █████╗ ███╗   ██╗ █████╗  ██████╗ ███████╗██████╗ 
echo ██║ ██╔╝██║████╗  ██║██║██╔════╝    ████╗ ████║██╔══██╗████╗  ██║██╔══██╗██╔════╝ ██╔════╝██╔══██╗
echo █████╔╝ ██║██╔██╗ ██║██║██║         ██╔████╔██║███████║██╔██╗ ██║███████║██║  ███╗█████╗  ██████╔╝
echo ██╔═██╗ ██║██║╚██╗██║██║██║         ██║╚██╔╝██║██╔══██║██║╚██╗██║██╔══██║██║   ██║██╔══╝  ██╔══██╗
echo ██║  ██╗██║██║ ╚████║██║╚██████╗    ██║ ╚═╝ ██║██║  ██║██║ ╚████║██║  ██║╚██████╔╝███████╗██║  ██║
echo ╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚═╝ ╚═════╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═══╝╚═╝  ╚═╝ ╚═════╝ ╚══════╝╚═╝  ╚═╝
echo.
echo                           🏥 نظام إدارة العيادات الطبية 🏥
echo                            مركز د. حمدي عواد الطبي
echo                              التشغيل النهائي المضمون
echo.

echo 🎯 هذا الملف يضمن تشغيل النظام بأي طريقة ممكنة!
echo.

REM فحص شامل للمتطلبات
echo 🔍 فحص شامل للمتطلبات...
echo.

set nodejs_ok=0
set npm_ok=0
set internet_ok=0
set demo_ok=0

REM فحص Node.js
where node >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ Node.js متوفر
    node --version
    set nodejs_ok=1
) else (
    echo ❌ Node.js غير متوفر
)

REM فحص npm
where npm >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ npm متوفر
    npm --version
    set npm_ok=1
) else (
    echo ❌ npm غير متوفر
)

REM فحص الإنترنت
ping -n 1 8.8.8.8 >nul 2>&1
if %errorlevel% equ 0 (
    echo ✅ الإنترنت متوفر
    set internet_ok=1
) else (
    echo ❌ الإنترنت غير متوفر
)

REM فحص العرض التوضيحي
if exist demo.html (
    echo ✅ العرض التوضيحي متوفر
    set demo_ok=1
) else (
    echo ❌ العرض التوضيحي غير متوفر
)

echo.
echo ═══════════════════════════════════════════════════════════════════════
echo                          اختيار طريقة التشغيل
echo ═══════════════════════════════════════════════════════════════════════
echo.

REM تحديد أفضل طريقة تشغيل
if %nodejs_ok%==1 if %npm_ok%==1 if %internet_ok%==1 (
    echo 🎉 جميع المتطلبات متوفرة - يمكن تشغيل النظام الكامل!
    echo.
    echo [1] 🚀 تشغيل النظام الكامل (موصى به)
    echo [2] 🔧 إصلاح مشاكل التثبيت أولاً
    echo [3] 📦 تثبيت تدريجي آمن
    if %demo_ok%==1 echo [4] 🎭 العرض التوضيحي
    echo [5] ❌ خروج
    
    set /p choice="اختر الطريقة المفضلة (1-5): "
    
    if "%choice%"=="1" goto :full_run
    if "%choice%"=="2" goto :fix_install
    if "%choice%"=="3" goto :gradual_install
    if "%choice%"=="4" if %demo_ok%==1 goto :demo
    if "%choice%"=="5" goto :exit
    
) else if %nodejs_ok%==1 if %npm_ok%==1 (
    echo ⚠️ Node.js و npm متوفران لكن لا يوجد إنترنت
    echo.
    echo [1] 🔄 المحاولة بدون إنترنت
    if %demo_ok%==1 echo [2] 🎭 العرض التوضيحي
    echo [3] 📋 عرض التعليمات
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :offline_run
    if "%choice%"=="2" if %demo_ok%==1 goto :demo
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
    
) else if %demo_ok%==1 (
    echo 🎭 Node.js غير متوفر، لكن العرض التوضيحي متاح
    echo.
    echo [1] 🎭 فتح العرض التوضيحي
    echo [2] 📥 تحميل Node.js
    echo [3] 📋 عرض التعليمات
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :demo
    if "%choice%"=="2" goto :download_nodejs
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
    
) else (
    echo 🆘 لا توجد طريقة مباشرة للتشغيل
    echo.
    echo [1] 📥 تحميل Node.js
    echo [2] 🔧 إنشاء عرض بسيط
    echo [3] 📋 عرض التعليمات الكاملة
    echo [4] ❌ خروج
    
    set /p choice="اختر الطريقة (1-4): "
    
    if "%choice%"=="1" goto :download_nodejs
    if "%choice%"=="2" goto :create_simple
    if "%choice%"=="3" goto :show_instructions
    if "%choice%"=="4" goto :exit
)

echo ❌ خيار غير صحيح
timeout /t 2 >nul
goto :main

:end
echo.
echo 🎉 شكراً لاستخدام نظام إدارة العيادات الطبية
pause
goto :main

:exit
echo.
echo 👋 إلى اللقاء!
timeout /t 2 >nul
exit
